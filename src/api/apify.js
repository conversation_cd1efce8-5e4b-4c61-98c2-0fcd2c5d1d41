import axios from 'axios';

// Create a single cancellation token source for all requests
let cancelTokenSource = null;

// Backend API configuration
const apiConfig = {
  baseUrl: import.meta.env.VITE_API_URL,
};

/**
 * Fetches ads data from backend server
 * @param {string} searchTerm - Search keyword
 * @param {string} country - Country code
 * @param {number} maxItems - Maximum number of items to fetch
 * @param {string} pageId - Optional page ID for specific advertiser
 * @returns {Promise<Array>} Promise resolving to array of ads
 */
export const fetchAdsData = async (
  searchTerm = "Ecommerce",
  country = "GB",
  maxItems = 20,
  pageId = ""
) => {
  try {
    console.log(`Fetching ${maxItems} ads for "${searchTerm}" in ${country}...`);

    // Cancel previous request if it exists
    if (cancelTokenSource) {
      cancelTokenSource.cancel('Operation canceled due to new request');
    }

    // Create a new cancellation token
    cancelTokenSource = axios.CancelToken.source();

    // Make request to backend API with cancellation token
    const params = {
      query: searchTerm,
      country: country,
      maxItems: maxItems
    }

    // Add pageId if provided (for specific advertiser search)
    if (pageId) {
      params.pageId = pageId
    }

    const response = await axios.get(`${apiConfig.baseUrl}/ads`, {
      params,
      cancelToken: cancelTokenSource.token
    });

    console.log(`Retrieved ${response.data.data.length} ads from backend`);
    return response.data.data;
  } catch (error) {
    // Don't log or return mock data if the request was canceled
    if (axios.isCancel(error)) {
      console.log('Request canceled:', error.message);
      return [];
    }

    console.error("Error fetching ads data:", error.message);
    console.warn("Falling back to mock data due to API error");
    return mockData.slice(0, maxItems);
  }
};

/**
 * Fetches advertiser suggestions based on search term
 * @param {string} searchTerm - Search keyword
 * @param {string} country - Country code
 * @returns {Promise<Array>} Promise resolving to array of advertiser suggestions
 */
export const fetchAdvertiserSuggestions = async (
  searchTerm = "",
  country = "GB"
) => {
  try {
    if (!searchTerm || searchTerm.length < 2) {
      return [];
    }

    console.log(`Fetching advertiser suggestions for "${searchTerm}" in ${country}...`);

    // Cancel previous request if it exists (ensures only one request at a time)
    if (cancelTokenSource) {
      cancelTokenSource.cancel('Operation canceled due to new request');
    }

    // Create a new cancellation token
    cancelTokenSource = axios.CancelToken.source();

    // Make request to backend API for advertiser suggestions
    const response = await axios.get(`${apiConfig.baseUrl}/ads`, {
      params: {
        query: searchTerm,
        country: country,
        maxItems: 50 // Get more ads to extract unique pages from
      },
      cancelToken: cancelTokenSource.token
    });

    console.log(`Retrieved ${response.data.data.length} advertiser suggestions`);
    return response.data.data;
  } catch (error) {
    // Don't log or return mock data if the request was canceled
    if (axios.isCancel(error)) {
      console.log('Suggestions request canceled:', error.message);
      return [];
    }

    console.error("Error fetching advertiser suggestions:", error.message);
    console.warn("Falling back to mock ads data due to API error");
    return mockAdsData.filter(ad =>
      ad.page_name.toLowerCase().includes(searchTerm.toLowerCase())
    ).slice(0, 20);
  }
};

// Mock data for development/testing purposes
const mockData = [
  // {
  //   "ad_archive_id": "567233033103915",
  //   "page_id": "427897563737746",
  //   "snapshot": {
  //     "page_name": "Voyd Creative",
  //     "display_format": "CAROUSEL",
  //     "link_url": "http://instagram.com/voyd.creative",
  //     "page_categories": ["Business", "Business", "Business"],
  //     "title": "Voyd Creative"
  //   },
  //   "is_active": true,
  //   "start_date": 1747810800,
  //   "end_date": 1747810800,
  //   "url": "https://www.facebook.com/ads/library/?active_status=active&ad_type=all&country=GB&q=ecommerce&search_type=keyword_unordered&media_type=all"
  // }
];

// Mock ads data for suggestions (simulating ads with page_name and page_id)
const mockAdsData = [
  { ad_archive_id: "567233033103915", page_id: "427897563737746", page_name: "Voyd Creative" },
  { ad_archive_id: "567233033103916", page_id: "123456789012345", page_name: "Amazon" },
  { ad_archive_id: "567233033103917", page_id: "234567890123456", page_name: "Nike" },
  { ad_archive_id: "567233033103918", page_id: "345678901234567", page_name: "Apple" },
  { ad_archive_id: "567233033103919", page_id: "456789012345678", page_name: "Google" },
  { ad_archive_id: "567233033103920", page_id: "567890123456789", page_name: "Microsoft" },
  { ad_archive_id: "567233033103921", page_id: "678901234567890", page_name: "Facebook" },
  { ad_archive_id: "567233033103922", page_id: "789012345678901", page_name: "Tesla" },
  { ad_archive_id: "567233033103923", page_id: "890123456789012", page_name: "Netflix" },
  { ad_archive_id: "567233033103924", page_id: "901234567890123", page_name: "Spotify" },
  // Add some duplicate page_ids to test uniqueness
  { ad_archive_id: "567233033103925", page_id: "427897563737746", page_name: "Voyd Creative" },
  { ad_archive_id: "567233033103926", page_id: "123456789012345", page_name: "Amazon" },
];
