import axios from 'axios';

// Create a cancellation token source
let cancelTokenSource = null;

// Backend API configuration
const apiConfig = {
  baseUrl: import.meta.env.VITE_API_URL,
};

/**
 * Fetches ads data from backend server
 * @param {string} searchTerm - Search keyword
 * @param {string} country - Country code
 * @param {number} maxItems - Maximum number of items to fetch
 * @returns {Promise<Array>} Promise resolving to array of ads
 */
export const fetchAdsData = async (
  searchTerm = "Ecommerce",
  country = "GB",
  maxItems = 20
) => {
  try {
    console.log(`Fetching ${maxItems} ads for "${searchTerm}" in ${country}...`);

    // Cancel previous request if it exists
    if (cancelTokenSource) {
      cancelTokenSource.cancel('Operation canceled due to new request');
    }

    // Create a new cancellation token
    cancelTokenSource = axios.CancelToken.source();

    // Make request to backend API with cancellation token
    const response = await axios.get(`${apiConfig.baseUrl}/ads`, {
      params: {
        query: searchTerm,
        country: country,
        maxItems: maxItems
      },
      cancelToken: cancelTokenSource.token
    });
    
    console.log(`Retrieved ${response.data.data.length} ads from backend`);
    return response.data.data;
  } catch (error) {
    // Don't log or return mock data if the request was canceled
    if (axios.isCancel(error)) {
      console.log('Request canceled:', error.message);
      return [];
    }
    
    console.error("Error fetching ads data:", error.message);
    console.warn("Falling back to mock data due to API error");
    return mockData.slice(0, maxItems);
  }
};

// Mock data for development/testing purposes
const mockData = [
  // {
  //   "ad_archive_id": "567233033103915",
  //   "page_id": "427897563737746",
  //   "snapshot": {
  //     "page_name": "Voyd Creative",
  //     "display_format": "CAROUSEL",
  //     "link_url": "http://instagram.com/voyd.creative",
  //     "page_categories": ["Business", "Business", "Business"],
  //     "title": "Voyd Creative"
  //   },
  //   "is_active": true,
  //   "start_date": 1747810800,
  //   "end_date": 1747810800,
  //   "url": "https://www.facebook.com/ads/library/?active_status=active&ad_type=all&country=GB&q=ecommerce&search_type=keyword_unordered&media_type=all"
  // }
];
