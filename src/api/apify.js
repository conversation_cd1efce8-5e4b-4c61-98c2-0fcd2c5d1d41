import axios from 'axios';

// Create cancellation token sources for different requests
let cancelTokenSource = null;
let suggestionsCancelTokenSource = null;

// Backend API configuration
const apiConfig = {
  baseUrl: import.meta.env.VITE_API_URL,
};

/**
 * Fetches ads data from backend server
 * @param {string} searchTerm - Search keyword
 * @param {string} country - Country code
 * @param {number} maxItems - Maximum number of items to fetch
 * @returns {Promise<Array>} Promise resolving to array of ads
 */
export const fetchAdsData = async (
  searchTerm = "Ecommerce",
  country = "GB",
  maxItems = 20
) => {
  try {
    console.log(`Fetching ${maxItems} ads for "${searchTerm}" in ${country}...`);

    // Cancel previous request if it exists
    if (cancelTokenSource) {
      cancelTokenSource.cancel('Operation canceled due to new request');
    }

    // Create a new cancellation token
    cancelTokenSource = axios.CancelToken.source();

    // Make request to backend API with cancellation token
    const response = await axios.get(`${apiConfig.baseUrl}/ads`, {
      params: {
        query: searchTerm,
        country: country,
        maxItems: maxItems
      },
      cancelToken: cancelTokenSource.token
    });

    console.log(`Retrieved ${response.data.data.length} ads from backend`);
    return response.data.data;
  } catch (error) {
    // Don't log or return mock data if the request was canceled
    if (axios.isCancel(error)) {
      console.log('Request canceled:', error.message);
      return [];
    }

    console.error("Error fetching ads data:", error.message);
    console.warn("Falling back to mock data due to API error");
    return mockData.slice(0, maxItems);
  }
};

/**
 * Fetches advertiser suggestions based on search term
 * @param {string} searchTerm - Search keyword
 * @param {string} country - Country code
 * @returns {Promise<Array>} Promise resolving to array of advertiser suggestions
 */
export const fetchAdvertiserSuggestions = async (
  searchTerm = "",
  country = "GB"
) => {
  try {
    if (!searchTerm || searchTerm.length < 2) {
      return [];
    }

    console.log(`Fetching advertiser suggestions for "${searchTerm}" in ${country}...`);

    // Cancel previous suggestions request if it exists
    if (suggestionsCancelTokenSource) {
      suggestionsCancelTokenSource.cancel('Operation canceled due to new request');
    }

    // Create a new cancellation token for suggestions
    suggestionsCancelTokenSource = axios.CancelToken.source();

    // Make request to backend API for advertiser suggestions
    const response = await axios.get(`${apiConfig.baseUrl}/ads`, {
      params: {
        query: searchTerm,
        country: country,
        limit: 10 // Limit suggestions to 10 items
      },
      cancelToken: suggestionsCancelTokenSource.token
    });

    console.log(`Retrieved ${response.data.data.length} advertiser suggestions`);
    return response.data.data;
  } catch (error) {
    // Don't log or return mock data if the request was canceled
    if (axios.isCancel(error)) {
      console.log('Suggestions request canceled:', error.message);
      return [];
    }

    console.error("Error fetching advertiser suggestions:", error.message);
    console.warn("Falling back to mock suggestions due to API error");
    return mockAdvertiserSuggestions.filter(advertiser =>
      advertiser.name.toLowerCase().includes(searchTerm.toLowerCase())
    ).slice(0, 10);
  }
};

// Mock data for development/testing purposes
const mockData = [
  // {
  //   "ad_archive_id": "567233033103915",
  //   "page_id": "427897563737746",
  //   "snapshot": {
  //     "page_name": "Voyd Creative",
  //     "display_format": "CAROUSEL",
  //     "link_url": "http://instagram.com/voyd.creative",
  //     "page_categories": ["Business", "Business", "Business"],
  //     "title": "Voyd Creative"
  //   },
  //   "is_active": true,
  //   "start_date": 1747810800,
  //   "end_date": 1747810800,
  //   "url": "https://www.facebook.com/ads/library/?active_status=active&ad_type=all&country=GB&q=ecommerce&search_type=keyword_unordered&media_type=all"
  // }
];

// Mock advertiser suggestions for development/testing purposes
const mockAdvertiserSuggestions = [
  { id: "427897563737746", name: "Voyd Creative" },
  { id: "123456789012345", name: "Amazon" },
  { id: "234567890123456", name: "Nike" },
  { id: "345678901234567", name: "Apple" },
  { id: "456789012345678", name: "Google" },
  { id: "567890123456789", name: "Microsoft" },
  { id: "678901234567890", name: "Facebook" },
  { id: "789012345678901", name: "Tesla" },
  { id: "890123456789012", name: "Netflix" },
  { id: "901234567890123", name: "Spotify" },
];
