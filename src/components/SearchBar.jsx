import { useState, useEffect, useRef } from 'react'
import { FaSearch } from 'react-icons/fa'
import { fetchAdvertiserSuggestions } from '../api/apify'

// Countries list for dropdown
const countries = [
  { code: 'GB', name: 'United Kingdom' },
  { code: 'US', name: 'United States' },
  { code: 'CA', name: 'Canada' },
  { code: 'AU', name: 'Australia' },
  { code: 'DE', name: 'Germany' },
  { code: 'FR', name: 'France' },
]

// Items per page options
const itemsOptions = [10, 20, 50, 100]

function SearchBar({ onSearch, initialSearchTerm, initialCountry, initialMaxItems }) {
  const [suggestionQuery, setSuggestionQuery] = useState('') // For advertiser suggestions
  const [adsQuery, setAdsQuery] = useState(initialSearchTerm || '') // For ads search
  const [country, setCountry] = useState(initialCountry || 'GB')
  const [maxItems, setMaxItems] = useState(initialMaxItems || 20)
  const [suggestions, setSuggestions] = useState([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [loadingSuggestions, setLoadingSuggestions] = useState(false)
  const [selectedPageId, setSelectedPageId] = useState('')

  const debounceRef = useRef(null)
  const suggestionsRef = useRef(null)

  // Debounced effect to fetch suggestions only (minimal API calls)
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    debounceRef.current = setTimeout(() => {
      if (suggestionQuery.trim() && suggestionQuery.length >= 2) {
        fetchSuggestions(suggestionQuery, country)
      } else if (suggestionQuery.length === 0) {
        setSuggestions([])
        setShowSuggestions(false)
        setSelectedPageId('')
      }
    }, 500) // 500ms debounce

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [suggestionQuery, country])

  // Fetch advertiser suggestions
  const fetchSuggestions = async (term, selectedCountry) => {
    if (term.length < 2) {
      setSuggestions([])
      setShowSuggestions(false)
      return
    }

    setLoadingSuggestions(true)
    try {
      const suggestionData = await fetchAdvertiserSuggestions(term, selectedCountry)
      console.log({ suggestionData })

      // API returns array of {page_id, page_name} directly
      if (suggestionData && Array.isArray(suggestionData)) {
        const formattedSuggestions = suggestionData.map(suggestion => ({
          id: suggestion.page_id,
          name: suggestion.page_name
        }))

        setSuggestions(formattedSuggestions)
        setShowSuggestions(formattedSuggestions.length > 0)
      } else {
        setSuggestions([])
        setShowSuggestions(false)
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error)
      setSuggestions([])
    } finally {
      setLoadingSuggestions(false)
    }
  }

  // Handle suggestion click - store page_id for search button
  const handleSuggestionClick = (suggestion) => {
    setSuggestionQuery(suggestion.name)
    setSelectedPageId(suggestion.id)
    setShowSuggestions(false)
  }

  // Handle suggestion input change
  const handleSuggestionQueryChange = (e) => {
    setSuggestionQuery(e.target.value)
  }

  // Handle ads query input change
  const handleAdsQueryChange = (e) => {
    setAdsQuery(e.target.value)
  }

  // Handle country change
  const handleCountryChange = (e) => {
    setCountry(e.target.value)
  }

  // Handle max items change
  const handleMaxItemsChange = (e) => {
    setMaxItems(Number(e.target.value))
  }

  // Handle form submission (Search Ads button)
  const handleSubmit = (e) => {
    e.preventDefault()
    if (adsQuery.trim()) {
      // Send ads query, country, maxItems, and page_id if selected
      onSearch(adsQuery, country, maxItems, selectedPageId)
    }
  }

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target)) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Advertiser Suggestions Input */}
        <div className="relative" ref={suggestionsRef}>
          <label htmlFor="suggestionQuery" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Find Advertisers
          </label>
          <div className="relative">
            <input
              type="text"
              id="suggestionQuery"
              className="input w-full pl-10 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="e.g. Nike, Apple, Amazon"
              value={suggestionQuery}
              onChange={handleSuggestionQueryChange}
              onFocus={() => suggestions.length > 0 && setShowSuggestions(true)}
            />
            <FaSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />

            {/* Loading indicator for suggestions */}
            {loadingSuggestions && (
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              </div>
            )}
          </div>

          {/* Advertiser Suggestions Dropdown */}
          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto">
              <div className="py-1">
                <div className="px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                  Advertiser Suggestions
                </div>
                {suggestions.map((suggestion) => (
                  <button
                    key={suggestion.id}
                    type="button"
                    className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700"
                    onClick={() => handleSuggestionClick(suggestion)}
                  >
                    <div className="flex items-center">
                      <FaSearch className="mr-2 text-gray-400 text-xs" />
                      {suggestion.name}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Ads Search Input */}
        <div>
          <label htmlFor="adsQuery" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Search Ads Query
          </label>
          <div className="relative">
            <input
              type="text"
              id="adsQuery"
              className="input w-full pl-10 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="e.g. shoes, technology, food"
              value={adsQuery}
              onChange={handleAdsQueryChange}
              required
            />
            <FaSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

        <div>
          <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Country
          </label>
          <select
            id="country"
            className="select w-full dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            value={country}
            onChange={handleCountryChange}
          >
            {countries.map(country => (
              <option key={country.code} value={country.code}>
                {country.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="maxItems" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Results to Fetch
          </label>
          <select
            id="maxItems"
            className="select w-full dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            value={maxItems}
            onChange={handleMaxItemsChange}
          >
            {itemsOptions.map(option => (
              <option key={option} value={option}>
                {option} ads
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Search Ads Button */}
      <div className="flex justify-end">
        <button type="submit" className="btn btn-primary">
          Search Ads
        </button>
      </div>

      {/* Instructions */}
      <div className="text-center text-sm text-gray-500 dark:text-gray-400">
        Type to see advertiser suggestions, then click "Search Ads" to find ads
        {selectedPageId && <span className="text-blue-600 dark:text-blue-400"> (Selected advertiser will be used)</span>}
      </div>
    </form>
  )
}

export default SearchBar
