import { useState } from 'react'
import { FaSearch } from 'react-icons/fa'

// Countries list for dropdown
const countries = [
  { code: 'GB', name: 'United Kingdom' },
  { code: 'US', name: 'United States' },
  { code: 'CA', name: 'Canada' },
  { code: 'AU', name: 'Australia' },
  { code: 'DE', name: 'Germany' },
  { code: 'FR', name: 'France' },
]

// Items per page options
const itemsOptions = [10, 20, 50, 100]

function SearchBar({ onSearch, initialSearchTerm, initialCountry, initialMaxItems }) {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm || '')
  const [country, setCountry] = useState(initialCountry || 'GB')
  const [maxItems, setMaxItems] = useState(initialMaxItems || 20)

  const handleSubmit = (e) => {
    e.preventDefault()
    onSearch(searchTerm, country, maxItems)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="relative">
          <label htmlFor="searchTerm" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Search Keyword
          </label>
          <div className="relative">
            <input
              type="text"
              id="searchTerm"
              className="input w-full pl-10 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="e.g. Ecommerce"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              required
            />
            <FaSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          </div>
        </div>
        
        <div>
          <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Country
          </label>
          <select
            id="country"
            className="select w-full dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            value={country}
            onChange={(e) => setCountry(e.target.value)}
          >
            {countries.map(country => (
              <option key={country.code} value={country.code}>
                {country.name}
              </option>
            ))}
          </select>
        </div>
        
        <div>
          <label htmlFor="maxItems" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Results to Fetch
          </label>
          <select
            id="maxItems"
            className="select w-full dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            value={maxItems}
            onChange={(e) => setMaxItems(Number(e.target.value))}
          >
            {itemsOptions.map(option => (
              <option key={option} value={option}>
                {option} ads
              </option>
            ))}
          </select>
        </div>
      </div>
      
      <div className="flex justify-end">
        <button type="submit" className="btn btn-primary">
          Search Ads
        </button>
      </div>
    </form>
  )
}

export default SearchBar
