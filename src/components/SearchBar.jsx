import { useState, useEffect, useRef } from 'react'
import { FaSearch } from 'react-icons/fa'
import { fetchAdvertiserSuggestions } from '../api/apify'

// Countries list for dropdown
const countries = [
  { code: 'GB', name: 'United Kingdom' },
  { code: 'US', name: 'United States' },
  { code: 'CA', name: 'Canada' },
  { code: 'AU', name: 'Australia' },
  { code: 'DE', name: 'Germany' },
  { code: 'FR', name: 'France' },
]

// Items per page options
const itemsOptions = [10, 20, 50, 100]

function SearchBar({ onSearch, initialSearchTerm, initialCountry, initialMaxItems }) {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm || '')
  const [country, setCountry] = useState(initialCountry || 'GB')
  const [maxItems, setMaxItems] = useState(initialMaxItems || 20)
  const [suggestions, setSuggestions] = useState([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [loadingSuggestions, setLoadingSuggestions] = useState(false)

  const debounceRef = useRef(null)
  const suggestionsRef = useRef(null)

  // Debounced search effect
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    debounceRef.current = setTimeout(() => {
      if (searchTerm.trim() && searchTerm.length >= 2) {
        onSearch(searchTerm, country, maxItems)
        fetchSuggestions(searchTerm, country)
      } else if (searchTerm.length === 0) {
        setSuggestions([])
        setShowSuggestions(false)
      }
    }, 500) // 500ms debounce

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [searchTerm, country, maxItems, onSearch])

  // Fetch advertiser suggestions
  const fetchSuggestions = async (term, selectedCountry) => {
    if (term.length < 2) {
      setSuggestions([])
      setShowSuggestions(false)
      return
    }

    setLoadingSuggestions(true)
    try {
      const suggestionData = await fetchAdvertiserSuggestions(term, selectedCountry)
      setSuggestions(suggestionData || [])
      setShowSuggestions(true)
    } catch (error) {
      console.error('Error fetching suggestions:', error)
      setSuggestions([])
    } finally {
      setLoadingSuggestions(false)
    }
  }

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    setSearchTerm(suggestion.name)
    setShowSuggestions(false)
    onSearch(suggestion.name, country, maxItems)
  }

  // Handle input change
  const handleSearchTermChange = (e) => {
    setSearchTerm(e.target.value)
  }

  // Handle country change
  const handleCountryChange = (e) => {
    setCountry(e.target.value)
  }

  // Handle max items change
  const handleMaxItemsChange = (e) => {
    setMaxItems(Number(e.target.value))
  }

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target)) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="relative" ref={suggestionsRef}>
          <label htmlFor="searchTerm" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Search Keyword
          </label>
          <div className="relative">
            <input
              type="text"
              id="searchTerm"
              className="input w-full pl-10 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="e.g. Ecommerce"
              value={searchTerm}
              onChange={handleSearchTermChange}
              onFocus={() => suggestions.length > 0 && setShowSuggestions(true)}
            />
            <FaSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />

            {/* Loading indicator for suggestions */}
            {loadingSuggestions && (
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              </div>
            )}
          </div>

          {/* Advertiser Suggestions Dropdown */}
          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto">
              <div className="py-1">
                <div className="px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                  Advertiser Suggestions
                </div>
                {suggestions.map((suggestion) => (
                  <button
                    key={suggestion.id}
                    type="button"
                    className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700"
                    onClick={() => handleSuggestionClick(suggestion)}
                  >
                    <div className="flex items-center">
                      <FaSearch className="mr-2 text-gray-400 text-xs" />
                      {suggestion.name}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        <div>
          <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Country
          </label>
          <select
            id="country"
            className="select w-full dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            value={country}
            onChange={handleCountryChange}
          >
            {countries.map(country => (
              <option key={country.code} value={country.code}>
                {country.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="maxItems" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Results to Fetch
          </label>
          <select
            id="maxItems"
            className="select w-full dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            value={maxItems}
            onChange={handleMaxItemsChange}
          >
            {itemsOptions.map(option => (
              <option key={option} value={option}>
                {option} ads
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Search automatically triggers on input change - no button needed */}
      <div className="text-center text-sm text-gray-500 dark:text-gray-400">
        Search automatically triggers as you type (minimum 2 characters)
      </div>
    </div>
  )
}

export default SearchBar
