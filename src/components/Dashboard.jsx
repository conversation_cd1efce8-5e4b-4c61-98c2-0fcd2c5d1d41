import { useState, useEffect } from 'react'
import SearchBar from './SearchBar'
import AdsList from './AdsList'
import Pagination from './Pagination'
import { fetchAdsData } from '../api/apify'
import LoadingSpinner from './LoadingSpinner'
import ErrorMessage from './ErrorMessage'

function Dashboard() {
  const [searchTerm, setSearchTerm] = useState('')
  const [country, setCountry] = useState('GB')
  const [maxItems, setMaxItems] = useState(20)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [ads, setAds] = useState([])
  const [itemsPerPage] = useState(5)
  const [hasSearched, setHasSearched] = useState(false)
  const [advertisers, setAdvertisers] = useState([])
  const [selectedAdvertiser, setSelectedAdvertiser] = useState('')
  const [allAds, setAllAds] = useState([]) // Store all ads before filtering

  const handleSearch = async (term, selectedCountry, selectedMaxItems) => {
    setSearchTerm(term)
    setCountry(selectedCountry)
    setMaxItems(selectedMaxItems)
    setCurrentPage(1)
    setHasSearched(true)
    setLoading(true)
    setAds([])
    setSelectedAdvertiser('') // Reset advertiser filter on new search
  }

  useEffect(() => {
    const loadAds = async () => {
      if (!hasSearched || !searchTerm) {
        setLoading(false)
        return
      }
      
      try {
        setError(null)
        
        const data = await fetchAdsData(searchTerm, country, maxItems)
        setAllAds(data || []) // Store all ads
        setAds(data || [])
        
        // Extract unique advertisers
        const uniqueAdvertisers = Array.from(
          new Set(data?.map(ad => ad.page_id))
        ).map(pageId => {
          const ad = data.find(ad => ad.page_id === pageId)
          return {
            id: pageId,
            name: ad?.page_name || 'Unknown Advertiser'
          }
        })
        
        setAdvertisers(uniqueAdvertisers)
        setTotalPages(Math.ceil((data?.length || 0) / itemsPerPage))
      } catch (err) {
        console.error('Error loading ads:', err)
        setError('Failed to load ads. Please try again later.')
      } finally {
        setLoading(false)
      }
    }

    loadAds()
  }, [searchTerm, country, maxItems, hasSearched])

  // Filter ads when advertiser selection changes
  useEffect(() => {
    if (selectedAdvertiser) {
      const filteredAds = allAds.filter(ad => ad.page_id === selectedAdvertiser)
      setAds(filteredAds)
      setTotalPages(Math.ceil((filteredAds?.length || 0) / itemsPerPage))
      setCurrentPage(1) // Reset to first page when filtering
    } else if (allAds.length > 0) {
      setAds(allAds) // Show all ads when no advertiser is selected
      setTotalPages(Math.ceil((allAds?.length || 0) / itemsPerPage))
    }
  }, [selectedAdvertiser, allAds, itemsPerPage])

  // Get current ads for pagination
  const indexOfLastAd = currentPage * itemsPerPage
  const indexOfFirstAd = indexOfLastAd - itemsPerPage
  const currentAds = ads.slice(indexOfFirstAd, indexOfLastAd)

  const paginate = (pageNumber) => setCurrentPage(pageNumber)

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Meta Ads Dashboard</h2>
        <SearchBar 
          onSearch={handleSearch} 
          initialSearchTerm={searchTerm}
          initialCountry={country}
          initialMaxItems={maxItems}
        />
      </div>

      {!hasSearched ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
          <p className="text-gray-600 dark:text-gray-400">
            Enter a search term and click "Search Ads" to see results
          </p>
        </div>
      ) : loading ? (
        <LoadingSpinner />
      ) : error ? (
        <ErrorMessage message={error} />
      ) : (
        <>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 md:mb-0">
                Results for "{searchTerm}" in {country}
              </h3>
              
              {/* Advertiser filter dropdown */}
              {advertisers.length > 0 && (
                <div className="w-full md:w-64">
                  <select
                    className="select w-full dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    value={selectedAdvertiser}
                    onChange={(e) => setSelectedAdvertiser(e.target.value)}
                  >
                    <option value="">All Advertisers</option>
                    {advertisers.map(advertiser => (
                      <option key={advertiser.id} value={advertiser.id}>
                        {advertiser.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>
            
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Showing {ads.length > 0 ? `${indexOfFirstAd + 1}-${Math.min(indexOfLastAd, ads.length)} of ${ads.length}` : '0'} ads
            </p>
            
            <AdsList ads={currentAds} />
            
            <Pagination 
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={paginate}
            />
          </div>
        </>
      )}
    </div>
  )
}

export default Dashboard
