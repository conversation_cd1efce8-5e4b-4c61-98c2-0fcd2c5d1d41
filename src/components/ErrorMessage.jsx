import { FaExclamationTriangle } from 'react-icons/fa'

function ErrorMessage({ message }) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
      <div className="flex flex-col items-center">
        <FaExclamationTriangle className="text-5xl text-error-500 mb-4" />
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Error Loading Data</h3>
        <p className="text-gray-600 dark:text-gray-400">{message}</p>
        <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
          Please check your connection and try again.
        </p>
      </div>
    </div>
  )
}

export default ErrorMessage